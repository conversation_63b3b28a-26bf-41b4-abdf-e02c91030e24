import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useWebsite, Element } from '../contexts/WebsiteContext';
import { useAuth } from '../contexts/AuthContext';
import { v4 as uuidv4 } from 'uuid';
import { 
  Palette, Save, Eye, Settings, ChevronDown, Type, Image, 
  Square, Layout, Layers, X, ArrowLeft, Globe, Menu, 
  AlignLeft, AlignCenter, AlignRight, Bold, Italic, Underline
} from 'lucide-react';
import ElementRenderer from '../components/ElementRenderer';
import { SketchPicker } from 'react-color';

const Editor: React.FC = () => {
  const { websiteId, pageId } = useParams<{ websiteId: string; pageId: string }>();
  const { 
    fetchWebsite, 
    currentWebsite, 
    setCurrentWebsite, 
    currentPage, 
    setCurrentPage,
    addElement,
    updateElement,
    deleteElement,
    savePage,
    publishWebsite
  } = useWebsite();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [selectedElement, setSelectedElement] = useState<Element | null>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [activeTab, setActiveTab] = useState<'elements' | 'styles' | 'pages'>('elements');
  
  useEffect(() => {
    if (!websiteId) return;
    
    const loadWebsite = async () => {
      try {
        const website = await fetchWebsite(websiteId);
        setCurrentWebsite(website);
        
        if (pageId) {
          const page = website.pages.find(p => p.id === pageId);
          if (page) {
            setCurrentPage(page);
          } else {
            // If page not found, redirect to the first page
            if (website.pages.length > 0) {
              navigate(`/editor/${websiteId}/${website.pages[0].id}`);
            }
          }
        } else if (website.pages.length > 0) {
          // If no page specified, redirect to the first page
          navigate(`/editor/${websiteId}/${website.pages[0].id}`);
        }
      } catch (error) {
        console.error('Error loading website:', error);
      }
    };
    
    loadWebsite();
  }, [websiteId, pageId, fetchWebsite, setCurrentWebsite, setCurrentPage, navigate]);
  
  const handleAddElement = (type: 'text' | 'image' | 'button' | 'container' | 'section') => {
    if (!pageId) return;
    
    let newElement: Element;
    
    switch (type) {
      case 'text':
        newElement = {
          id: uuidv4(),
          type: 'text',
          content: 'Edit this text',
          style: {
            fontSize: '16px',
            color: '#333333',
            fontWeight: 'normal',
            textAlign: 'left',
            padding: '10px'
          }
        };
        break;
        
      case 'image':
        newElement = {
          id: uuidv4(),
          type: 'image',
          src: 'https://images.pexels.com/photos/1591447/pexels-photo-1591447.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
          alt: 'Image description',
          style: {
            width: '100%',
            borderRadius: '4px',
            margin: '10px 0'
          }
        };
        break;
        
      case 'button':
        newElement = {
          id: uuidv4(),
          type: 'button',
          content: 'Click Me',
          style: {
            backgroundColor: '#4F46E5',
            color: '#FFFFFF',
            padding: '10px 20px',
            borderRadius: '4px',
            fontWeight: 'bold',
            textAlign: 'center',
            margin: '10px 0'
          }
        };
        break;
        
      case 'container':
        newElement = {
          id: uuidv4(),
          type: 'container',
          style: {
            padding: '20px',
            backgroundColor: '#F9FAFB',
            borderRadius: '8px',
            margin: '10px 0'
          },
          children: []
        };
        break;
        
      case 'section':
        newElement = {
          id: uuidv4(),
          type: 'section',
          style: {
            padding: '40px 20px',
            backgroundColor: '#FFFFFF',
            margin: '0 0 20px 0'
          },
          children: []
        };
        break;
        
      default:
        return;
    }
    
    addElement(pageId, newElement);
  };
  
  const handleSave = async () => {
    if (!websiteId || !pageId || !currentWebsite || !currentPage) return;
    
    setIsSaving(true);
    try {
      await savePage(websiteId, pageId);
      alert('Page saved successfully!');
    } catch (error) {
      console.error('Error saving page:', error);
      alert('Failed to save page');
    } finally {
      setIsSaving(false);
    }
  };
  
  const handlePublish = async () => {
    if (!websiteId || !currentWebsite) return;
    
    setIsPublishing(true);
    try {
      await publishWebsite(websiteId);
      alert('Website published successfully!');
    } catch (error) {
      console.error('Error publishing website:', error);
      alert('Failed to publish website');
    } finally {
      setIsPublishing(false);
    }
  };
  
  const handleElementSelect = (element: Element) => {
    setSelectedElement(element);
    setActiveTab('styles');
  };
  
  const handleElementUpdate = (updates: Partial<Element>) => {
    if (!pageId || !selectedElement) return;
    updateElement(pageId, selectedElement.id, updates);
  };
  
  const handleElementDelete = () => {
    if (!pageId || !selectedElement) return;
    deleteElement(pageId, selectedElement.id);
    setSelectedElement(null);
  };
  
  const handleTextAlign = (align: 'left' | 'center' | 'right') => {
    if (!selectedElement) return;
    handleElementUpdate({
      style: {
        ...selectedElement.style,
        textAlign: align
      }
    });
  };
  
  const handleFontWeight = (weight: 'normal' | 'bold') => {
    if (!selectedElement) return;
    handleElementUpdate({
      style: {
        ...selectedElement.style,
        fontWeight: weight
      }
    });
  };
  
  const handleColorChange = (color: any) => {
    if (!selectedElement) return;
    handleElementUpdate({
      style: {
        ...selectedElement.style,
        color: color.hex
      }
    });
  };
  
  const handleBackgroundColorChange = (color: any) => {
    if (!selectedElement) return;
    handleElementUpdate({
      style: {
        ...selectedElement.style,
        backgroundColor: color.hex
      }
    });
  };
  
  const navigateToPage = (pageId: string) => {
    if (!websiteId) return;
    navigate(`/editor/${websiteId}/${pageId}`);
  };
  
  if (!currentWebsite || !currentPage) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }
  
  return (
    <div className="flex h-screen overflow-hidden bg-gray-100">
      {/* Sidebar */}
      <div className={`bg-white shadow-md z-20 transition-all duration-300 ${sidebarOpen ? 'w-64' : 'w-0'}`}>
        {sidebarOpen && (
          <div className="h-full flex flex-col">
            {/* Sidebar Header */}
            <div className="p-4 border-b flex items-center justify-between">
              <div className="flex items-center">
                <Palette className="h-6 w-6 text-indigo-600 mr-2" />
                <h2 className="font-semibold text-gray-800 truncate">
                  {currentWebsite.name}
                </h2>
              </div>
              <button 
                onClick={() => setSidebarOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={18} />
              </button>
            </div>
            
            {/* Tabs */}
            <div className="flex border-b">
              <button
                className={`flex-1 py-3 text-sm font-medium ${activeTab === 'elements' ? 'text-indigo-600 border-b-2 border-indigo-600' : 'text-gray-600 hover:text-gray-800'}`}
                onClick={() => setActiveTab('elements')}
              >
                Elements
              </button>
              <button
                className={`flex-1 py-3 text-sm font-medium ${activeTab === 'styles' ? 'text-indigo-600 border-b-2 border-indigo-600' : 'text-gray-600 hover:text-gray-800'}`}
                onClick={() => setActiveTab('styles')}
              >
                Styles
              </button>
              <button
                className={`flex-1 py-3 text-sm font-medium ${activeTab === 'pages' ? 'text-indigo-600 border-b-2 border-indigo-600' : 'text-gray-600 hover:text-gray-800'}`}
                onClick={() => setActiveTab('pages')}
              >
                Pages
              </button>
            </div>
            
            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto p-4">
              {activeTab === 'elements' && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Add Elements</h3>
                  
                  <button
                    onClick={() => handleAddElement('text')}
                    className="w-full flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <Type className="h-5 w-5 text-gray-600 mr-3" />
                    <span className="text-sm">Text</span>
                  </button>
                  
                  <button
                    onClick={() => handleAddElement('image')}
                    className="w-full flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <Image className="h-5 w-5 text-gray-600 mr-3" />
                    <span className="text-sm">Image</span>
                  </button>
                  
                  <button
                    onClick={() => handleAddElement('button')}
                    className="w-full flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <Square className="h-5 w-5 text-gray-600 mr-3" />
                    <span className="text-sm">Button</span>
                  </button>
                  
                  <button
                    onClick={() => handleAddElement('container')}
                    className="w-full flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <Layout className="h-5 w-5 text-gray-600 mr-3" />
                    <span className="text-sm">Container</span>
                  </button>
                  
                  <button
                    onClick={() => handleAddElement('section')}
                    className="w-full flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <Layers className="h-5 w-5 text-gray-600 mr-3" />
                    <span className="text-sm">Section</span>
                  </button>
                </div>
              )}
              
              {activeTab === 'styles' && selectedElement && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Element Styles</h3>
                  
                  {/* Element Type */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-xs text-gray-500 mb-1">Element Type</p>
                    <p className="text-sm font-medium text-gray-800 capitalize">{selectedElement.type}</p>
                  </div>
                  
                  {/* Text Alignment (for text elements) */}
                  {selectedElement.type === 'text' && (
                    <div>
                      <p className="text-xs text-gray-500 mb-2">Text Alignment</p>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleTextAlign('left')}
                          className={`p-2 rounded ${selectedElement.style.textAlign === 'left' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-600'}`}
                        >
                          <AlignLeft size={16} />
                        </button>
                        <button
                          onClick={() => handleTextAlign('center')}
                          className={`p-2 rounded ${selectedElement.style.textAlign === 'center' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-600'}`}
                        >
                          <AlignCenter size={16} />
                        </button>
                        <button
                          onClick={() => handleTextAlign('right')}
                          className={`p-2 rounded ${selectedElement.style.textAlign === 'right' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-600'}`}
                        >
                          <AlignRight size={16} />
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {/* Font Weight (for text elements) */}
                  {selectedElement.type === 'text' && (
                    <div>
                      <p className="text-xs text-gray-500 mb-2">Font Style</p>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleFontWeight('bold')}
                          className={`p-2 rounded ${selectedElement.style.fontWeight === 'bold' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-600'}`}
                        >
                          <Bold size={16} />
                        </button>
                        <button
                          className="p-2 rounded bg-gray-100 text-gray-600"
                        >
                          <Italic size={16} />
                        </button>
                        <button
                          className="p-2 rounded bg-gray-100 text-gray-600"
                        >
                          <Underline size={16} />
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {/* Font Size (for text elements) */}
                  {selectedElement.type === 'text' && (
                    <div>
                      <p className="text-xs text-gray-500 mb-2">Font Size</p>
                      <select
                        value={selectedElement.style.fontSize || '16px'}
                        onChange={(e) => handleElementUpdate({
                          style: { ...selectedElement.style, fontSize: e.target.value }
                        })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="12px">12px</option>
                        <option value="14px">14px</option>
                        <option value="16px">16px</option>
                        <option value="18px">18px</option>
                        <option value="20px">20px</option>
                        <option value="24px">24px</option>
                        <option value="32px">32px</option>
                        <option value="48px">48px</option>
                      </select>
                    </div>
                  )}
                  
                  {/* Text Color */}
                  {(selectedElement.type === 'text' || selectedElement.type === 'button') && (
                    <div>
                      <p className="text-xs text-gray-500 mb-2">Text Color</p>
                      <button
                        onClick={() => setShowColorPicker(!showColorPicker)}
                        className="w-full flex items-center justify-between p-2 border border-gray-300 rounded-md"
                      >
                        <div className="flex items-center">
                          <div 
                            className="w-4 h-4 rounded-sm mr-2" 
                            style={{ backgroundColor: selectedElement.style.color || '#000000' }}
                          ></div>
                          <span className="text-sm">{selectedElement.style.color || '#000000'}</span>
                        </div>
                        <ChevronDown size={16} />
                      </button>
                      {showColorPicker && (
                        <div className="mt-2 relative z-10">
                          <div 
                            className="fixed inset-0" 
                            onClick={() => setShowColorPicker(false)}
                          ></div>
                          <div className="absolute">
                            <SketchPicker
                              color={selectedElement.style.color || '#000000'}
                              onChange={handleColorChange}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Background Color */}
                  {(selectedElement.type === 'button' || selectedElement.type === 'container' || selectedElement.type === 'section') && (
                    <div>
                      <p className="text-xs text-gray-500 mb-2">Background Color</p>
                      <button
                        onClick={() => setShowColorPicker(!showColorPicker)}
                        className="w-full flex items-center justify-between p-2 border border-gray-300 rounded-md"
                      >
                        <div className="flex items-center">
                          <div 
                            className="w-4 h-4 rounded-sm mr-2" 
                            style={{ backgroundColor: selectedElement.style.backgroundColor || 'transparent' }}
                          ></div>
                          <span className="text-sm">{selectedElement.style.backgroundColor || 'transparent'}</span>
                        </div>
                        <ChevronDown size={16} />
                      </button>
                      {showColorPicker && (
                        <div className="mt-2 relative z-10">
                          <div 
                            className="fixed inset-0" 
                            onClick={() => setShowColorPicker(false)}
                          ></div>
                          <div className="absolute">
                            <SketchPicker
                              color={selectedElement.style.backgroundColor || 'transparent'}
                              onChange={handleBackgroundColorChange}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Delete Element Button */}
                  <button
                    onClick={handleElementDelete}
                    className="w-full mt-4 bg-red-50 text-red-600 border border-red-200 rounded-md py-2 hover:bg-red-100 transition-colors"
                  >
                    Delete Element
                  </button>
                </div>
              )}
              
              {activeTab === 'pages' && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Website Pages</h3>
                  
                  {currentWebsite.pages.map(page => (
                    <button
                      key={page.id}
                      onClick={() => navigateToPage(page.id)}
                      className={`w-full flex items-center p-3 rounded-lg border ${page.id === currentPage.id ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'} transition-colors`}
                    >
                      <span className={`text-sm ${page.id === currentPage.id ? 'font-medium text-indigo-700' : 'text-gray-700'}`}>
                        {page.name}
                      </span>
                    </button>
                  ))}
                </div>
              )}
            </div>
            
            {/* Sidebar Footer */}
            <div className="p-4 border-t">
              <Link
                to="/"
                className="flex items-center text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft size={16} className="mr-2" />
                <span className="text-sm">Back to Dashboard</span>
              </Link>
            </div>
          </div>
        )}
      </div>
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {/* Toolbar */}
        <div className="bg-white shadow-sm border-b flex items-center justify-between p-3 z-10">
          {!sidebarOpen && (
            <button
              onClick={() => setSidebarOpen(true)}
              className="text-gray-600 hover:text-gray-800 mr-4"
            >
              <Menu size={20} />
            </button>
          )}
          
          <div className="flex items-center">
            <h1 className="text-lg font-medium text-gray-800 mr-2">
              {currentPage.name}
            </h1>
            <span className="text-sm text-gray-500">
              {currentWebsite.published ? 'Published' : 'Draft'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Link
              to={`/preview/${websiteId}/${pageId}`}
              className="flex items-center px-3 py-1.5 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              target="_blank"
            >
              <Eye size={16} className="mr-1" />
              Preview
            </Link>
            
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center px-3 py-1.5 text-sm text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
            >
              {isSaving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-1"></div>
              ) : (
                <Save size={16} className="mr-1" />
              )}
              Save
            </button>
            
            <button
              onClick={handlePublish}
              disabled={isPublishing}
              className="flex items-center px-3 py-1.5 text-sm text-white bg-green-600 rounded-md hover:bg-green-700"
            >
              {isPublishing ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-1"></div>
              ) : (
                <Globe size={16} className="mr-1" />
              )}
              Publish
            </button>
          </div>
        </div>
        
        {/* Canvas */}
        <div className="flex-1 overflow-y-auto bg-gray-200 p-4">
          <div className="bg-white min-h-full shadow-sm mx-auto max-w-4xl">
            {currentPage.elements.length === 0 ? (
              <div className="flex flex-col items-center justify-center min-h-[400px] border-2 border-dashed border-gray-300 rounded-lg p-8">
                <Palette size={48} className="text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-700 mb-2">This page is empty</h3>
                <p className="text-gray-500 text-center mb-4">
                  Start building your page by adding elements from the sidebar
                </p>
                <button
                  onClick={() => {
                    setSidebarOpen(true);
                    setActiveTab('elements');
                  }}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Add Elements
                </button>
              </div>
            ) : (
              <div className="p-4">
                {currentPage.elements.map(element => (
                  <ElementRenderer
                    key={element.id}
                    element={element}
                    onSelect={handleElementSelect}
                    selected={selectedElement?.id === element.id}
                    onUpdate={(updates) => updateElement(pageId!, element.id, updates)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Editor;