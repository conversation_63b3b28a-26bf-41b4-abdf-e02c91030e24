import mongoose from 'mongoose';

const elementSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: ['text', 'image', 'button', 'container', 'section']
  },
  content: String,
  src: String,
  alt: String,
  style: {
    color: String,
    backgroundColor: String,
    fontSize: String,
    fontWeight: String,
    fontFamily: String,
    textAlign: String,
    padding: String,
    margin: String,
    borderRadius: String,
    width: String,
    height: String
  },
  children: {
    type: [this],
    default: []
  }
}, { _id: false });

const pageSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  slug: {
    type: String,
    required: true
  },
  elements: {
    type: [elementSchema],
    default: []
  },
  seo: {
    title: String,
    description: String,
    keywords: String
  }
}, { _id: false });

const websiteSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  pages: {
    type: [pageSchema],
    default: []
  },
  published: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt timestamp before saving
websiteSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const Website = mongoose.model('Website', websiteSchema);

export default Website;