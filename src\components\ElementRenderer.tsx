import React, { useState } from 'react';
import { Element } from '../contexts/WebsiteContext';
import ContentEditable from 'react-contenteditable';

interface ElementRendererProps {
  element: Element;
  onSelect: (element: Element) => void;
  selected: boolean;
  onUpdate: (updates: Partial<Element>) => void;
}

const ElementRenderer: React.FC<ElementRendererProps> = ({ 
  element, 
  onSelect, 
  selected,
  onUpdate
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(element);
  };
  
  const handleContentChange = (e: any) => {
    onUpdate({ content: e.target.value });
  };
  
  const renderElement = () => {
    switch (element.type) {
      case 'text':
        return (
          <ContentEditable
            html={element.content || ''}
            onChange={handleContentChange}
            className="outline-none w-full"
          />
        );
        
      case 'image':
        return (
          <img 
            src={element.src} 
            alt={element.alt || ''} 
            className="max-w-full h-auto"
          />
        );
        
      case 'button':
        return (
          <button className="cursor-pointer">
            {element.content}
          </button>
        );
        
      case 'container':
        return (
          <div className="w-full">
            {element.children?.map(child => (
              <ElementRenderer
                key={child.id}
                element={child}
                onSelect={onSelect}
                selected={selected}
                onUpdate={(updates) => {
                  // Handle updates to child elements
                }}
              />
            ))}
          </div>
        );
        
      case 'section':
        return (
          <div className="w-full">
            {element.children?.map(child => (
              <ElementRenderer
                key={child.id}
                element={child}
                onSelect={onSelect}
                selected={selected}
                onUpdate={(updates) => {
                  // Handle updates to child elements
                }}
              />
            ))}
          </div>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        ...element.style,
        position: 'relative',
        outline: selected ? '2px solid #4F46E5' : isHovered ? '1px dashed #9CA3AF' : 'none',
      }}
      className="relative group"
    >
      {renderElement()}
      
      {(selected || isHovered) && (
        <div className="absolute -top-6 left-0 bg-indigo-600 text-white text-xs py-1 px-2 rounded-t-md">
          {element.type}
        </div>
      )}
    </div>
  );
};

export default ElementRenderer;