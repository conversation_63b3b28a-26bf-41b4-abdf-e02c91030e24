import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import Dashboard from './pages/Dashboard';
import Editor from './pages/Editor';
import Preview from './pages/Preview';
import Login from './pages/Login';
import Register from './pages/Register';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';
import { WebsiteProvider } from './contexts/WebsiteContext';

function App() {
  return (
    <AuthProvider>
      <WebsiteProvider>
        <DndProvider backend={HTML5Backend}>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
              <Route path="/editor/:websiteId/:pageId" element={<ProtectedRoute><Editor /></ProtectedRoute>} />
              <Route path="/preview/:websiteId/:pageId" element={<ProtectedRoute><Preview /></ProtectedRoute>} />
              <Route path="/site/:websiteId/:pageId" element={<Preview />} />
            </Routes>
          </Router>
        </DndProvider>
      </WebsiteProvider>
    </AuthProvider>
  );
}

export default App;