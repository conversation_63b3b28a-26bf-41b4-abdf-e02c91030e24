import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useWebsite } from '../contexts/WebsiteContext';
import { Palette, Plus, Settings, Globe, Trash2, Edit } from 'lucide-react';

const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { websites, fetchWebsites, createWebsite, deleteWebsite } = useWebsite();
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [newWebsiteName, setNewWebsiteName] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const loadWebsites = async () => {
      try {
        await fetchWebsites();
      } catch (error) {
        console.error('Error loading websites:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadWebsites();
  }, [fetchWebsites]);

  const handleCreateWebsite = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newWebsiteName.trim()) return;

    setIsCreating(true);
    try {
      const newWebsite = await createWebsite(newWebsiteName);
      setNewWebsiteName('');
      setShowCreateModal(false);
      // Navigate to the editor for the home page of the new website
      const homePage = newWebsite.pages.find(page => page.slug === 'home');
      if (homePage) {
        navigate(`/editor/${newWebsite.id}/${homePage.id}`);
      }
    } catch (error) {
      console.error('Error creating website:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteWebsite = async (websiteId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (window.confirm('Are you sure you want to delete this website? This action cannot be undone.')) {
      try {
        await deleteWebsite(websiteId);
      } catch (error) {
        console.error('Error deleting website:', error);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Palette className="h-8 w-8 text-indigo-600 mr-2" />
            <h1 className="text-xl font-bold text-gray-900">Website Builder</h1>
          </div>
          <div className="flex items-center">
            <span className="mr-4 text-sm text-gray-600">
              {user?.name}
            </span>
            <button
              onClick={() => logout()}
              className="text-sm text-gray-600 hover:text-gray-900"
            >
              Sign Out
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900">My Websites</h2>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="h-5 w-5 mr-1" />
            New Website
          </button>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : websites.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="mx-auto w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
              <Palette className="h-8 w-8 text-indigo-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No websites yet</h3>
            <p className="text-gray-600 mb-6">Create your first website to get started</p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg inline-flex items-center"
            >
              <Plus className="h-5 w-5 mr-1" />
              Create Website
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {websites.map((website) => {
              const homePage = website.pages.find(page => page.slug === 'home');
              return (
                <div
                  key={website.id}
                  className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200"
                >
                  <div className="h-40 bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                    <Palette className="h-16 w-16 text-white opacity-75" />
                  </div>
                  <div className="p-5">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{website.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      {website.pages.length} pages • Last updated: {new Date(website.updatedAt).toLocaleDateString()}
                    </p>
                    <div className="flex space-x-2">
                      {homePage && (
                        <Link
                          to={`/editor/${website.id}/${homePage.id}`}
                          className="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Link>
                      )}
                      {website.published && (
                        <Link
                          to={`/site/${website.id}/home`}
                          className="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center"
                          target="_blank"
                        >
                          <Globe className="h-4 w-4 mr-1" />
                          View
                        </Link>
                      )}
                      <button
                        onClick={(e) => handleDeleteWebsite(website.id, e)}
                        className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-2 rounded text-sm font-medium flex items-center justify-center"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </main>

      {/* Create Website Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Website</h3>
              <form onSubmit={handleCreateWebsite}>
                <div className="mb-4">
                  <label htmlFor="websiteName" className="block text-sm font-medium text-gray-700 mb-1">
                    Website Name
                  </label>
                  <input
                    id="websiteName"
                    type="text"
                    value={newWebsiteName}
                    onChange={(e) => setNewWebsiteName(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="My Portfolio"
                    required
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isCreating}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center"
                  >
                    {isCreating ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
                    ) : (
                      <>
                        <Plus className="h-5 w-5 mr-1" />
                        Create
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;