import express from 'express';
import Website from '../models/Website.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Get all websites for the current user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const websites = await Website.find({ user: req.user.id });
    res.json(websites);
  } catch (error) {
    console.error('Get websites error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get a specific website
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const website = await Website.findOne({
      _id: req.params.id,
      user: req.user.id
    });
    
    if (!website) {
      return res.status(404).json({ message: 'Website not found' });
    }
    
    res.json(website);
  } catch (error) {
    console.error('Get website error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create a new website
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { name, pages } = req.body;
    
    const website = new Website({
      name,
      user: req.user.id,
      pages: pages || []
    });
    
    await website.save();
    res.status(201).json(website);
  } catch (error) {
    console.error('Create website error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update a website
router.patch('/:id', authenticateToken, async (req, res) => {
  try {
    const { name, pages, published } = req.body;
    
    const website = await Website.findOne({
      _id: req.params.id,
      user: req.user.id
    });
    
    if (!website) {
      return res.status(404).json({ message: 'Website not found' });
    }
    
    if (name) website.name = name;
    if (pages) website.pages = pages;
    if (published !== undefined) website.published = published;
    
    await website.save();
    res.json(website);
  } catch (error) {
    console.error('Update website error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete a website
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const website = await Website.findOneAndDelete({
      _id: req.params.id,
      user: req.user.id
    });
    
    if (!website) {
      return res.status(404).json({ message: 'Website not found' });
    }
    
    res.json({ message: 'Website deleted successfully' });
  } catch (error) {
    console.error('Delete website error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update a specific page
router.put('/:websiteId/pages/:pageId', authenticateToken, async (req, res) => {
  try {
    const website = await Website.findOne({
      _id: req.params.websiteId,
      user: req.user.id
    });
    
    if (!website) {
      return res.status(404).json({ message: 'Website not found' });
    }
    
    const pageIndex = website.pages.findIndex(page => page.id === req.params.pageId);
    
    if (pageIndex === -1) {
      return res.status(404).json({ message: 'Page not found' });
    }
    
    website.pages[pageIndex] = req.body;
    await website.save();
    
    res.json(website.pages[pageIndex]);
  } catch (error) {
    console.error('Update page error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Publish a website
router.post('/:id/publish', authenticateToken, async (req, res) => {
  try {
    const website = await Website.findOne({
      _id: req.params.id,
      user: req.user.id
    });
    
    if (!website) {
      return res.status(404).json({ message: 'Website not found' });
    }
    
    website.published = true;
    await website.save();
    
    res.json({ message: 'Website published successfully' });
  } catch (error) {
    console.error('Publish website error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get a published website (public route)
router.get('/public/:id', async (req, res) => {
  try {
    const website = await Website.findOne({
      _id: req.params.id,
      published: true
    });
    
    if (!website) {
      return res.status(404).json({ message: 'Website not found' });
    }
    
    res.json(website);
  } catch (error) {
    console.error('Get public website error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

export default router;