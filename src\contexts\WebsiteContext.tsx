import React, { createContext, useState, useContext } from 'react';
import { v4 as uuidv4 } from 'uuid';

// Types
export interface ElementStyle {
  color?: string;
  backgroundColor?: string;
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
  textAlign?: string;
  padding?: string;
  margin?: string;
  borderRadius?: string;
  width?: string;
  height?: string;
}

export interface Element {
  id: string;
  type: 'text' | 'image' | 'button' | 'container' | 'section';
  content?: string;
  src?: string;
  alt?: string;
  style: ElementStyle;
  children?: Element[];
}

export interface Page {
  id: string;
  name: string;
  slug: string;
  elements: Element[];
  seo?: {
    title: string;
    description: string;
    keywords: string;
  };
}

export interface Website {
  id: string;
  name: string;
  pages: Page[];
  createdAt: Date;
  updatedAt: Date;
  published: boolean;
}

interface WebsiteContextType {
  websites: Website[];
  currentWebsite: Website | null;
  currentPage: Page | null;
  setCurrentWebsite: (website: Website | null) => void;
  setCurrentPage: (page: Page | null) => void;
  createWebsite: (name: string) => Promise<Website>;
  updateWebsite: (websiteId: string, data: Partial<Website>) => Promise<Website>;
  deleteWebsite: (websiteId: string) => Promise<void>;
  fetchWebsites: () => Promise<void>;
  fetchWebsite: (websiteId: string) => Promise<Website>;
  addElement: (pageId: string, element: Element, parentId?: string) => void;
  updateElement: (pageId: string, elementId: string, updates: Partial<Element>) => void;
  deleteElement: (pageId: string, elementId: string) => void;
  moveElement: (pageId: string, elementId: string, newIndex: number, parentId?: string) => void;
  savePage: (websiteId: string, pageId: string) => Promise<void>;
  publishWebsite: (websiteId: string) => Promise<void>;
}

const WebsiteContext = createContext<WebsiteContextType | undefined>(undefined);

export const useWebsite = () => {
  const context = useContext(WebsiteContext);
  if (context === undefined) {
    throw new Error('useWebsite must be used within a WebsiteProvider');
  }
  return context;
};

// Helper function to find and update an element in the tree
const updateElementInTree = (elements: Element[], elementId: string, updates: Partial<Element>): Element[] => {
  return elements.map(el => {
    if (el.id === elementId) {
      return { ...el, ...updates };
    }
    if (el.children) {
      return {
        ...el,
        children: updateElementInTree(el.children, elementId, updates)
      };
    }
    return el;
  });
};

// Helper function to find and delete an element in the tree
const deleteElementFromTree = (elements: Element[], elementId: string): Element[] => {
  return elements.filter(el => {
    if (el.id === elementId) {
      return false;
    }
    if (el.children) {
      el.children = deleteElementFromTree(el.children, elementId);
    }
    return true;
  });
};

export const WebsiteProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [websites, setWebsites] = useState<Website[]>([]);
  const [currentWebsite, setCurrentWebsite] = useState<Website | null>(null);
  const [currentPage, setCurrentPage] = useState<Page | null>(null);

  const fetchWebsites = async () => {
    try {
      const response = await fetch('/api/websites', {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch websites');
      }
      
      const data = await response.json();
      setWebsites(data);
    } catch (error) {
      console.error('Error fetching websites:', error);
    }
  };

  const fetchWebsite = async (websiteId: string): Promise<Website> => {
    try {
      const response = await fetch(`/api/websites/${websiteId}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch website');
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching website:', error);
      throw error;
    }
  };

  const createWebsite = async (name: string): Promise<Website> => {
    try {
      // Default pages for new website
      const defaultPages = [
        {
          id: uuidv4(),
          name: 'Home',
          slug: 'home',
          elements: [],
          seo: {
            title: 'Home',
            description: 'Welcome to my website',
            keywords: 'website, portfolio'
          }
        },
        {
          id: uuidv4(),
          name: 'About',
          slug: 'about',
          elements: [],
          seo: {
            title: 'About',
            description: 'Learn about me',
            keywords: 'about, bio'
          }
        },
        {
          id: uuidv4(),
          name: 'Portfolio',
          slug: 'portfolio',
          elements: [],
          seo: {
            title: 'Portfolio',
            description: 'My work portfolio',
            keywords: 'portfolio, projects, work'
          }
        },
        {
          id: uuidv4(),
          name: 'Contact',
          slug: 'contact',
          elements: [],
          seo: {
            title: 'Contact',
            description: 'Get in touch with me',
            keywords: 'contact, email, form'
          }
        }
      ];

      const response = await fetch('/api/websites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, pages: defaultPages }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to create website');
      }
      
      const newWebsite = await response.json();
      setWebsites(prev => [...prev, newWebsite]);
      return newWebsite;
    } catch (error) {
      console.error('Error creating website:', error);
      throw error;
    }
  };

  const updateWebsite = async (websiteId: string, data: Partial<Website>): Promise<Website> => {
    try {
      const response = await fetch(`/api/websites/${websiteId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to update website');
      }
      
      const updatedWebsite = await response.json();
      
      setWebsites(prev => 
        prev.map(website => 
          website.id === websiteId ? updatedWebsite : website
        )
      );
      
      if (currentWebsite?.id === websiteId) {
        setCurrentWebsite(updatedWebsite);
      }
      
      return updatedWebsite;
    } catch (error) {
      console.error('Error updating website:', error);
      throw error;
    }
  };

  const deleteWebsite = async (websiteId: string): Promise<void> => {
    try {
      const response = await fetch(`/api/websites/${websiteId}`, {
        method: 'DELETE',
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete website');
      }
      
      setWebsites(prev => prev.filter(website => website.id !== websiteId));
      
      if (currentWebsite?.id === websiteId) {
        setCurrentWebsite(null);
        setCurrentPage(null);
      }
    } catch (error) {
      console.error('Error deleting website:', error);
      throw error;
    }
  };

  const addElement = (pageId: string, element: Element, parentId?: string) => {
    if (!currentWebsite) return;
    
    const updatedPages = currentWebsite.pages.map(page => {
      if (page.id !== pageId) return page;
      
      if (!parentId) {
        // Add to root level
        return {
          ...page,
          elements: [...page.elements, element]
        };
      } else {
        // Add to a parent element
        const updateElementWithChild = (elements: Element[]): Element[] => {
          return elements.map(el => {
            if (el.id === parentId) {
              return {
                ...el,
                children: [...(el.children || []), element]
              };
            }
            if (el.children) {
              return {
                ...el,
                children: updateElementWithChild(el.children)
              };
            }
            return el;
          });
        };
        
        return {
          ...page,
          elements: updateElementWithChild(page.elements)
        };
      }
    });
    
    setCurrentWebsite({
      ...currentWebsite,
      pages: updatedPages
    });
    
    // Update current page if it's the one being modified
    if (currentPage?.id === pageId) {
      const updatedPage = updatedPages.find(p => p.id === pageId);
      if (updatedPage) {
        setCurrentPage(updatedPage);
      }
    }
  };

  const updateElement = (pageId: string, elementId: string, updates: Partial<Element>) => {
    if (!currentWebsite) return;
    
    const updatedPages = currentWebsite.pages.map(page => {
      if (page.id !== pageId) return page;
      
      return {
        ...page,
        elements: updateElementInTree(page.elements, elementId, updates)
      };
    });
    
    setCurrentWebsite({
      ...currentWebsite,
      pages: updatedPages
    });
    
    // Update current page if it's the one being modified
    if (currentPage?.id === pageId) {
      const updatedPage = updatedPages.find(p => p.id === pageId);
      if (updatedPage) {
        setCurrentPage(updatedPage);
      }
    }
  };

  const deleteElement = (pageId: string, elementId: string) => {
    if (!currentWebsite) return;
    
    const updatedPages = currentWebsite.pages.map(page => {
      if (page.id !== pageId) return page;
      
      return {
        ...page,
        elements: deleteElementFromTree(page.elements, elementId)
      };
    });
    
    setCurrentWebsite({
      ...currentWebsite,
      pages: updatedPages
    });
    
    // Update current page if it's the one being modified
    if (currentPage?.id === pageId) {
      const updatedPage = updatedPages.find(p => p.id === pageId);
      if (updatedPage) {
        setCurrentPage(updatedPage);
      }
    }
  };

  const moveElement = (pageId: string, elementId: string, newIndex: number, parentId?: string) => {
    if (!currentWebsite) return;
    
    const updatedPages = currentWebsite.pages.map(page => {
      if (page.id !== pageId) return page;
      
      // Function to find and remove element from its current position
      const findAndRemoveElement = (elements: Element[]): [Element[], Element | null] => {
        let removedElement: Element | null = null;
        const newElements = elements.filter(el => {
          if (el.id === elementId) {
            removedElement = el;
            return false;
          }
          if (el.children) {
            const [newChildren, removed] = findAndRemoveElement(el.children);
            if (removed) {
              removedElement = removed;
              el.children = newChildren;
            }
          }
          return true;
        });
        return [newElements, removedElement];
      };
      
      // Find and remove the element from its current position
      const [newElements, removedElement] = findAndRemoveElement(page.elements);
      
      if (!removedElement) return page;
      
      // Function to insert element at new position
      const insertElement = (elements: Element[], targetId: string | undefined, index: number): Element[] => {
        if (!targetId) {
          // Insert at root level
          const result = [...elements];
          result.splice(index, 0, removedElement!);
          return result;
        }
        
        // Insert inside a parent
        return elements.map(el => {
          if (el.id === targetId) {
            const children = [...(el.children || [])];
            children.splice(index, 0, removedElement!);
            return { ...el, children };
          }
          if (el.children) {
            return {
              ...el,
              children: insertElement(el.children, targetId, index)
            };
          }
          return el;
        });
      };
      
      // Insert the element at its new position
      const finalElements = insertElement(newElements, parentId, newIndex);
      
      return {
        ...page,
        elements: finalElements
      };
    });
    
    setCurrentWebsite({
      ...currentWebsite,
      pages: updatedPages
    });
    
    // Update current page if it's the one being modified
    if (currentPage?.id === pageId) {
      const updatedPage = updatedPages.find(p => p.id === pageId);
      if (updatedPage) {
        setCurrentPage(updatedPage);
      }
    }
  };

  const savePage = async (websiteId: string, pageId: string): Promise<void> => {
    if (!currentWebsite) return;
    
    const page = currentWebsite.pages.find(p => p.id === pageId);
    if (!page) return;
    
    try {
      const response = await fetch(`/api/websites/${websiteId}/pages/${pageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(page),
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to save page');
      }
    } catch (error) {
      console.error('Error saving page:', error);
      throw error;
    }
  };

  const publishWebsite = async (websiteId: string): Promise<void> => {
    try {
      const response = await fetch(`/api/websites/${websiteId}/publish`, {
        method: 'POST',
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to publish website');
      }
      
      // Update local state
      setWebsites(prev => 
        prev.map(website => 
          website.id === websiteId ? { ...website, published: true } : website
        )
      );
      
      if (currentWebsite?.id === websiteId) {
        setCurrentWebsite({ ...currentWebsite, published: true });
      }
    } catch (error) {
      console.error('Error publishing website:', error);
      throw error;
    }
  };

  return (
    <WebsiteContext.Provider value={{
      websites,
      currentWebsite,
      currentPage,
      setCurrentWebsite,
      setCurrentPage,
      createWebsite,
      updateWebsite,
      deleteWebsite,
      fetchWebsites,
      fetchWebsite,
      addElement,
      updateElement,
      deleteElement,
      moveElement,
      savePage,
      publishWebsite
    }}>
      {children}
    </WebsiteContext.Provider>
  );
};