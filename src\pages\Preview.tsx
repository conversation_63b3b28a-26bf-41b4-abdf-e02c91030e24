import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useWebsite } from '../contexts/WebsiteContext';
import { ArrowLeft, Edit } from 'lucide-react';

const Preview: React.FC = () => {
  const { websiteId, pageId } = useParams<{ websiteId: string; pageId: string }>();
  const { 
    fetchWebsite, 
    currentWebsite, 
    setCurrentWebsite, 
    currentPage, 
    setCurrentPage 
  } = useWebsite();
  
  useEffect(() => {
    if (!websiteId) return;
    
    const loadWebsite = async () => {
      try {
        const website = await fetchWebsite(websiteId);
        setCurrentWebsite(website);
        
        if (pageId) {
          const page = website.pages.find(p => p.id === pageId);
          if (page) {
            setCurrentPage(page);
          }
        }
      } catch (error) {
        console.error('Error loading website:', error);
      }
    };
    
    loadWebsite();
  }, [websiteId, pageId, fetchWebsite, setCurrentWebsite, setCurrentPage]);
  
  if (!currentWebsite || !currentPage) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }
  
  // Function to render elements recursively
  const renderElement = (element: any) => {
    switch (element.type) {
      case 'text':
        return (
          <div key={element.id} style={element.style} dangerouslySetInnerHTML={{ __html: element.content }} />
        );
        
      case 'image':
        return (
          <img 
            key={element.id}
            src={element.src} 
            alt={element.alt || ''} 
            style={element.style}
          />
        );
        
      case 'button':
        return (
          <button key={element.id} style={element.style}>
            {element.content}
          </button>
        );
        
      case 'container':
      case 'section':
        return (
          <div key={element.id} style={element.style}>
            {element.children?.map((child: any) => renderElement(child))}
          </div>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Link
              to={pageId ? `/editor/${websiteId}/${pageId}` : '/'}
              className="flex items-center text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft size={16} className="mr-2" />
              <span>Back to Editor</span>
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            {currentWebsite.pages.map(page => (
              <Link
                key={page.id}
                to={`/preview/${websiteId}/${page.id}`}
                className={`text-sm ${page.id === currentPage.id ? 'font-medium text-indigo-600' : 'text-gray-600 hover:text-gray-800'}`}
              >
                {page.name}
              </Link>
            ))}
          </div>
          
          <div>
            <Link
              to={`/editor/${websiteId}/${pageId}`}
              className="flex items-center px-3 py-1.5 text-sm text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
            >
              <Edit size={16} className="mr-1" />
              Edit
            </Link>
          </div>
        </div>
      </nav>
      
      {/* Page Content */}
      <main className="max-w-4xl mx-auto bg-white min-h-screen shadow-sm">
        {currentPage.elements.length === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
            <h3 className="text-lg font-medium text-gray-700 mb-2">This page is empty</h3>
            <p className="text-gray-500 text-center mb-4">
              Go back to the editor to add content to this page
            </p>
            <Link
              to={`/editor/${websiteId}/${pageId}`}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              Edit Page
            </Link>
          </div>
        ) : (
          <div className="p-4">
            {currentPage.elements.map(element => renderElement(element))}
          </div>
        )}
      </main>
    </div>
  );
};

export default Preview;